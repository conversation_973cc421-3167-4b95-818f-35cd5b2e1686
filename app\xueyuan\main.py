# coding:utf-8
"""
学习工具主程序

该模块是学习工具的主程序入口，负责初始化应用程序、
加载配置、启动主界面等。

主要功能：
- 应用程序初始化
- 配置加载
- 主界面启动
- 异常处理

使用方法：
    python -m app.xueyuan.main
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QFont, QIcon
from qfluentwidgets import setTheme, Theme, setThemeColor, FluentTranslator

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.xueyuan.view import MainWindow
from app.xueyuan.common.config_loader import StudyConfigLoader
from app.xueyuan.database.manager import db_manager
from app.xueyuan.logging import log_manager


class StudyApplication:
    """
    学习工具应用程序类
    
    负责应用程序的初始化和启动
    """
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.config_loader = None
        
    def init_application(self):
        """初始化应用程序"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序属性
        self.app.setApplicationName("学习工具")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("学习工具开发团队")
        self.app.setOrganizationDomain("example.com")
        
        # 设置应用程序图标
        icon_path = project_root / "app" / "resources" / "icons" / "app.ico"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置高DPI支持
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps)
        
        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        self.app.setFont(font)
        
        print("[应用] 应用程序初始化完成")
    
    def init_config(self):
        """初始化配置"""
        try:
            self.config_loader = StudyConfigLoader()
            success = self.config_loader.load_config()
            
            if success:
                print("[应用] 配置加载成功")
            else:
                print("[应用] 配置加载失败，使用默认配置")
                
        except Exception as e:
            print(f"[应用] 配置初始化失败: {e}")
    
    def init_database(self):
        """初始化数据库"""
        try:
            success = db_manager.initialize_database()

            if success:
                print("[应用] 数据库初始化成功")
            else:
                print("[应用] 数据库初始化失败")
                return False

        except Exception as e:
            print(f"[应用] 数据库初始化失败: {e}")
            return False
        
        return True
    
    def init_logging(self):
        """初始化日志系统"""
        try:
            # 日志系统会在导入时自动初始化
            log_manager.info("学习工具应用程序启动", module="Application")
            print("[应用] 日志系统初始化成功")
            
        except Exception as e:
            print(f"[应用] 日志系统初始化失败: {e}")
    
    def init_theme(self):
        """初始化主题"""
        try:
            # 设置默认主题
            setTheme(Theme.LIGHT)
            
            # 设置主题色
            setThemeColor('#0078d4')
            
            print("[应用] 主题初始化成功")
            
        except Exception as e:
            print(f"[应用] 主题初始化失败: {e}")
    
    def init_translator(self):
        """初始化翻译器"""
        try:
            # 创建翻译器
            translator = FluentTranslator(QLocale.Chinese)
            self.app.installTranslator(translator)
            
            print("[应用] 翻译器初始化成功")
            
        except Exception as e:
            print(f"[应用] 翻译器初始化失败: {e}")
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            self.main_window = MainWindow()
            print("[应用] 主窗口创建成功")
            
        except Exception as e:
            print(f"[应用] 主窗口创建失败: {e}")
            log_manager.error(f"主窗口创建失败: {e}", module="Application")
            return False
        
        return True
    
    def show_main_window(self):
        """显示主窗口"""
        if self.main_window:
            self.main_window.show()
            print("[应用] 主窗口已显示")
            log_manager.info("主窗口已显示", module="Application")
    
    def run(self):
        """运行应用程序"""
        try:
            print("=" * 50)
            print("学习工具 - 自动化学习辅助系统")
            print("版本: 1.0.0")
            print("=" * 50)
            
            # 初始化各个组件
            print("[应用] 开始初始化...")
            
            self.init_application()
            self.init_config()
            self.init_logging()
            
            # 初始化数据库
            if not self.init_database():
                print("[应用] 数据库初始化失败，程序退出")
                return 1
            
            self.init_theme()
            self.init_translator()
            
            # 创建并显示主窗口
            if not self.create_main_window():
                print("[应用] 主窗口创建失败，程序退出")
                return 1
            
            self.show_main_window()
            
            print("[应用] 初始化完成，程序启动")
            log_manager.info("学习工具启动完成", module="Application")
            
            # 运行应用程序
            return self.app.exec()
            
        except KeyboardInterrupt:
            print("\n[应用] 用户中断程序")
            log_manager.info("用户中断程序", module="Application")
            return 0
            
        except Exception as e:
            print(f"[应用] 程序运行异常: {e}")
            log_manager.error(f"程序运行异常: {e}", module="Application")
            return 1
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            print("[应用] 开始清理资源...")
            
            # 关闭数据库连接
            if db_manager:
                db_manager.close()
                print("[应用] 数据库连接已关闭")
            
            # 保存配置
            if self.config_loader:
                self.config_loader.save_config()
                print("[应用] 配置已保存")
            
            log_manager.info("学习工具正常退出", module="Application")
            print("[应用] 资源清理完成")
            
        except Exception as e:
            print(f"[应用] 资源清理失败: {e}")


def main():
    """主函数"""
    app = StudyApplication()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
